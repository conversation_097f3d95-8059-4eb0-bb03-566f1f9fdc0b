/**
 * @file Motor_Control.c
 * @brief 纯手动电机控制接口实现
 * @version 2.0
 * @date 2025-08-01
 *
 * 提供最简单直接的电机控制接口，完全手动控制，无PID
 */

#include "Motor_Control.h"
#include "Motor.h"

/**
 * @brief 停止所有电机
 */
void MotorControl_Stop(void)
{
    Motor_StopAll();
}

/**
 * @brief 前进
 * @param speed 前进速度 (0~100)
 */
void MotorControl_Forward(float speed)
{
    // 限制速度范围
    if (speed > 100.0f) speed = 100.0f;
    if (speed < 0.0f) speed = 0.0f;
    
    Motor_SetBothSpeed(speed, speed);
}

/**
 * @brief 后退
 * @param speed 后退速度 (0~100)
 */
void MotorControl_Backward(float speed)
{
    // 限制速度范围
    if (speed > 100.0f) speed = 100.0f;
    if (speed < 0.0f) speed = 0.0f;
    
    Motor_SetBothSpeed(-speed, -speed);
}

/**
 * @brief 左转
 * @param speed 转向速度 (0~100)
 */
void MotorControl_TurnLeft(float speed)
{
    // 限制速度范围
    if (speed > 100.0f) speed = 100.0f;
    if (speed < 0.0f) speed = 0.0f;
    
    // 左转：左电机慢速或反转，右电机正常速度
    Motor_SetBothSpeed(speed * 0.3f, speed);
}

/**
 * @brief 右转
 * @param speed 转向速度 (0~100)
 */
void MotorControl_TurnRight(float speed)
{
    // 限制速度范围
    if (speed > 100.0f) speed = 100.0f;
    if (speed < 0.0f) speed = 0.0f;
    
    // 右转：左电机正常速度，右电机慢速或反转
    Motor_SetBothSpeed(speed, speed * 0.3f);
}

/**
 * @brief 分别设置左右电机速度
 * @param left_speed 左电机速度 (-100~100)
 * @param right_speed 右电机速度 (-100~100)
 */
void MotorControl_SetSpeed(float left_speed, float right_speed)
{
    Motor_SetBothSpeed(left_speed, right_speed);
}

/**
 * @brief 获取左电机当前设定速度
 * @return 左电机速度值
 */
float MotorControl_GetLeftSpeed(void)
{
    return Motor_Left.Current_Speed;
}

/**
 * @brief 获取右电机当前设定速度
 * @return 右电机速度值
 */
float MotorControl_GetRightSpeed(void)
{
    return Motor_Right.Current_Speed;
}
