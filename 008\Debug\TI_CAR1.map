******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 20:42:12 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001359


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001b50  0001e4b0  R  X
  SRAM                  20200000   00008000  00000341  00007cbf  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001b50   00001b50    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000019c0   000019c0    r-x .text
  00001a80    00001a80    00000080   00000080    r-- .rodata
  00001b00    00001b00    00000050   00000050    r-- .cinit
20200000    20200000    00000141   00000000    rw-
  20200000    20200000    000000f4   00000000    rw- .bss
  202000f4    202000f4    0000004d   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000019c0     
                  000000c0    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002a0    000001b0     Task.o (.text.Task_Start)
                  00000450    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000554    000000f0     Motor.o (.text.Motor_SetDirc)
                  00000644    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000720    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  000007d4    000000a8     Motor.o (.text.Motor_SetSpeed)
                  0000087c    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000091c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000009a8    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00000a2c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00000ab0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000b2c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00000b90    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000bf4    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000c56    00000002     Task_App.o (.text.Task_IdleFunction)
                  00000c58    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000cb4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00000d0c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00000d5c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00000da8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00000df4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00000e40    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00000e8c    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00000ed6    00000002     --HOLE-- [fill = 0]
                  00000ed8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000f20    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00000f64    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00000fa6    00000002     --HOLE-- [fill = 0]
                  00000fa8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00000fe8    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001028    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00001064    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000010a0    0000003c     libclang_rt.builtins.a : comparesf2.S.obj (.text.__gtsf2)
                  000010dc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001118    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001152    00000002     --HOLE-- [fill = 0]
                  00001154    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001188    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000011b8    0000002c     Interrupt.o (.text.Interrupt_Init)
                  000011e4    0000002c     Motor.o (.text.Motor_Start)
                  00001210    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000123c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00001268    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001290    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000012b8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000012e0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00001308    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00001330    00000028     SysTick.o (.text.SysTick_Increasment)
                  00001358    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001380    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000013a6    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000013cc    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000013f0    00000024     Motor.o (.text.Motor_StopAll)
                  00001414    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001434    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001454    00000020     main.o (.text.main)
                  00001474    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001492    00000002     --HOLE-- [fill = 0]
                  00001494    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000014b0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000014cc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000014e8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001504    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00001520    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000153c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001558    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00001574    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001590    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000015ac    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000015c8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000015e4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001600    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00001618    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00001630    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00001648    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001660    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001678    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001690    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000016a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000016c0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000016d8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000016f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001708    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001720    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001738    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001750    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001768    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001780    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001798    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  000017b0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  000017c8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  000017e0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000017f8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001810    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001828    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001840    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00001858    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00001870    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00001888    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  000018a0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000018b8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000018d0    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000018e8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000018fe    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00001914    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000192a    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001940    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00001954    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001968    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000197c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001990    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000019a4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000019b8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000019ca    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000019dc    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000019ee    00000002     --HOLE-- [fill = 0]
                  000019f0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001a00    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001a10    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001a20    00000010     Task_App.o (.text.Task_Init)
                  00001a30    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00001a3c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00001a48    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001a52    00000008     Interrupt.o (.text.SysTick_Handler)
                  00001a5a    00000002     --HOLE-- [fill = 0]
                  00001a5c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001a64    00000006     libc.a : exit.c.obj (.text:abort)
                  00001a6a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001a6e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001a72    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001a76    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001a7a    00000006     --HOLE-- [fill = 0]

.cinit     0    00001b00    00000050     
                  00001b00    0000002c     (.cinit..data.load) [load image, compression = lzss]
                  00001b2c    0000000c     (__TI_handler_table)
                  00001b38    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001b40    00000010     (__TI_cinit_table)

.rodata    0    00001a80    00000080     
                  00001a80    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001aa8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00001ac0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00001ad8    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00001ae2    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001ae4    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00001aec    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00001af4    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00001af7    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001af9    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00001afb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000f4     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    00000004     (.common:ExISR_Flag)

.data      0    202000f4    0000004d     UNINITIALIZED
                  202000f4    00000020     Motor.o (.data.Motor_Left)
                  20200114    00000020     Motor.o (.data.Motor_Right)
                  20200134    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200138    00000004     SysTick.o (.data.delayTick)
                  2020013c    00000004     SysTick.o (.data.uwTick)
                  20200140    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             3426   123       0      
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         3466   315       0      
                                                              
    .\APP\Src\
       Interrupt.o                    378    0         4      
       Task_App.o                     18     0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         396    0         8      
                                                              
    .\BSP\Src\
       Task.o                         432    0         241    
       Motor.o                        556    0         64     
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1040   0         313    
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1022   0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       extendsfdf2.S.obj              64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         354    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      80        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   6574   395       833    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001b40 records: 2, size/record: 8, table size: 16
	.data: load addr=00001b00, load size=0000002c bytes, run addr=202000f4, run size=0000004d bytes, compression=lzss
	.bss: load addr=00001b38, load size=00000008 bytes, run addr=20200000, run size=000000f4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001b2c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001a6b  ADC0_IRQHandler                      
00001a6b  ADC1_IRQHandler                      
00001a6b  AES_IRQHandler                       
00001a6e  C$$EXIT                              
00001a6b  CANFD0_IRQHandler                    
00001a6b  DAC0_IRQHandler                      
00000fa9  DL_ADC12_setClockConfig              
00001a49  DL_Common_delayCycles                
00000da9  DL_DMA_initChannel                   
000013a7  DL_I2C_setClockConfig                
00000645  DL_SYSCTL_configSYSPLL               
00000b2d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000f21  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000451  DL_Timer_initFourCCPWMMode           
000015ad  DL_Timer_setCaptCompUpdateMethod     
00001829  DL_Timer_setCaptureCompareOutCtl     
00001a01  DL_Timer_setCaptureCompareValue      
000015c9  DL_Timer_setClockConfig              
00000ed9  DL_UART_init                         
000019b9  DL_UART_setClockConfig               
00001a6b  DMA_IRQHandler                       
20200134  Data_MotorEncoder                    
00001a6b  Default_Handler                      
202000f0  ExISR_Flag                           
00001a6b  GROUP0_IRQHandler                    
00000721  GROUP1_IRQHandler                    
00001a6f  HOSTexit                             
00001a6b  HardFault_Handler                    
00001a6b  I2C0_IRQHandler                      
00001a6b  I2C1_IRQHandler                      
000011b9  Interrupt_Init                       
202000f4  Motor_Left                           
20200114  Motor_Right                          
000007d5  Motor_SetSpeed                       
000011e5  Motor_Start                          
000013f1  Motor_StopAll                        
00001a6b  NMI_Handler                          
00001a6b  PendSV_Handler                       
00001a6b  RTC_IRQHandler                       
00001a73  Reset_Handler                        
00001a6b  SPI0_IRQHandler                      
00001a6b  SPI1_IRQHandler                      
00001a6b  SVC_Handler                          
00000e41  SYSCFG_DL_ADC1_init                  
00001189  SYSCFG_DL_DMA_CH_RX_init             
000018d1  SYSCFG_DL_DMA_CH_TX_init             
00001a31  SYSCFG_DL_DMA_init                   
000000c1  SYSCFG_DL_GPIO_init                  
00000cb5  SYSCFG_DL_I2C_MPU6050_init           
00000b91  SYSCFG_DL_I2C_OLED_init              
0000091d  SYSCFG_DL_Motor_PWM_init             
00000c59  SYSCFG_DL_SYSCTL_init                
00001a11  SYSCFG_DL_SYSTICK_init               
000009a9  SYSCFG_DL_UART0_init                 
00001211  SYSCFG_DL_init                       
0000087d  SYSCFG_DL_initPower                  
00001a53  SysTick_Handler                      
00001331  SysTick_Increasment                  
00001a3d  Sys_GetTick                          
00001a6b  TIMA0_IRQHandler                     
00001a6b  TIMA1_IRQHandler                     
00001a6b  TIMG0_IRQHandler                     
00001a6b  TIMG12_IRQHandler                    
00001a6b  TIMG6_IRQHandler                     
00001a6b  TIMG7_IRQHandler                     
00001a6b  TIMG8_IRQHandler                     
000019cb  TI_memcpy_small                      
00000c57  Task_IdleFunction                    
00001a21  Task_Init                            
000002a1  Task_Start                           
00001a6b  UART0_IRQHandler                     
00001a6b  UART1_IRQHandler                     
00001a6b  UART2_IRQHandler                     
00001a6b  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001b40  __TI_CINIT_Base                      
00001b50  __TI_CINIT_Limit                     
00001b50  __TI_CINIT_Warm                      
00001b2c  __TI_Handler_Table_Base              
00001b38  __TI_Handler_Table_Limit             
000010dd  __TI_auto_init_nobinit_nopinit       
00000ab1  __TI_decompress_lzss                 
000019dd  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000192b  __TI_zero_init_nomemset              
00000f65  __aeabi_d2uiz                        
00000fe9  __aeabi_f2d                          
00000bf5  __aeabi_fcmpeq                       
00000c31  __aeabi_fcmpge                       
00000c45  __aeabi_fcmpgt                       
00000c1d  __aeabi_fcmple                       
00000c09  __aeabi_fcmplt                       
00001a5d  __aeabi_memcpy                       
00001a5d  __aeabi_memcpy4                      
00001a5d  __aeabi_memcpy8                      
ffffffff  __binit__                            
00001119  __cmpsf2                             
00001119  __eqsf2                              
00000fe9  __extendsfdf2                        
00000f65  __fixunsdfsi                         
000010a1  __gesf2                              
000010a1  __gtsf2                              
00001119  __lesf2                              
00001119  __ltsf2                              
UNDEFED   __mpu_init                           
00001119  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001359  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001a77  _system_pre_init                     
00001a65  abort                                
ffffffff  binit                                
20200138  delayTick                            
00000000  interruptVectors                     
00001455  main                                 
2020013c  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
000002a1  Task_Start                           
00000451  DL_Timer_initFourCCPWMMode           
00000645  DL_SYSCTL_configSYSPLL               
00000721  GROUP1_IRQHandler                    
000007d5  Motor_SetSpeed                       
0000087d  SYSCFG_DL_initPower                  
0000091d  SYSCFG_DL_Motor_PWM_init             
000009a9  SYSCFG_DL_UART0_init                 
00000ab1  __TI_decompress_lzss                 
00000b2d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000b91  SYSCFG_DL_I2C_OLED_init              
00000bf5  __aeabi_fcmpeq                       
00000c09  __aeabi_fcmplt                       
00000c1d  __aeabi_fcmple                       
00000c31  __aeabi_fcmpge                       
00000c45  __aeabi_fcmpgt                       
00000c57  Task_IdleFunction                    
00000c59  SYSCFG_DL_SYSCTL_init                
00000cb5  SYSCFG_DL_I2C_MPU6050_init           
00000da9  DL_DMA_initChannel                   
00000e41  SYSCFG_DL_ADC1_init                  
00000ed9  DL_UART_init                         
00000f21  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000f65  __aeabi_d2uiz                        
00000f65  __fixunsdfsi                         
00000fa9  DL_ADC12_setClockConfig              
00000fe9  __aeabi_f2d                          
00000fe9  __extendsfdf2                        
000010a1  __gesf2                              
000010a1  __gtsf2                              
000010dd  __TI_auto_init_nobinit_nopinit       
00001119  __cmpsf2                             
00001119  __eqsf2                              
00001119  __lesf2                              
00001119  __ltsf2                              
00001119  __nesf2                              
00001189  SYSCFG_DL_DMA_CH_RX_init             
000011b9  Interrupt_Init                       
000011e5  Motor_Start                          
00001211  SYSCFG_DL_init                       
00001331  SysTick_Increasment                  
00001359  _c_int00_noargs                      
000013a7  DL_I2C_setClockConfig                
000013f1  Motor_StopAll                        
00001455  main                                 
000015ad  DL_Timer_setCaptCompUpdateMethod     
000015c9  DL_Timer_setClockConfig              
00001829  DL_Timer_setCaptureCompareOutCtl     
000018d1  SYSCFG_DL_DMA_CH_TX_init             
0000192b  __TI_zero_init_nomemset              
000019b9  DL_UART_setClockConfig               
000019cb  TI_memcpy_small                      
000019dd  __TI_decompress_none                 
00001a01  DL_Timer_setCaptureCompareValue      
00001a11  SYSCFG_DL_SYSTICK_init               
00001a21  Task_Init                            
00001a31  SYSCFG_DL_DMA_init                   
00001a3d  Sys_GetTick                          
00001a49  DL_Common_delayCycles                
00001a53  SysTick_Handler                      
00001a5d  __aeabi_memcpy                       
00001a5d  __aeabi_memcpy4                      
00001a5d  __aeabi_memcpy8                      
00001a65  abort                                
00001a6b  ADC0_IRQHandler                      
00001a6b  ADC1_IRQHandler                      
00001a6b  AES_IRQHandler                       
00001a6b  CANFD0_IRQHandler                    
00001a6b  DAC0_IRQHandler                      
00001a6b  DMA_IRQHandler                       
00001a6b  Default_Handler                      
00001a6b  GROUP0_IRQHandler                    
00001a6b  HardFault_Handler                    
00001a6b  I2C0_IRQHandler                      
00001a6b  I2C1_IRQHandler                      
00001a6b  NMI_Handler                          
00001a6b  PendSV_Handler                       
00001a6b  RTC_IRQHandler                       
00001a6b  SPI0_IRQHandler                      
00001a6b  SPI1_IRQHandler                      
00001a6b  SVC_Handler                          
00001a6b  TIMA0_IRQHandler                     
00001a6b  TIMA1_IRQHandler                     
00001a6b  TIMG0_IRQHandler                     
00001a6b  TIMG12_IRQHandler                    
00001a6b  TIMG6_IRQHandler                     
00001a6b  TIMG7_IRQHandler                     
00001a6b  TIMG8_IRQHandler                     
00001a6b  UART0_IRQHandler                     
00001a6b  UART1_IRQHandler                     
00001a6b  UART2_IRQHandler                     
00001a6b  UART3_IRQHandler                     
00001a6e  C$$EXIT                              
00001a6f  HOSTexit                             
00001a73  Reset_Handler                        
00001a77  _system_pre_init                     
00001b2c  __TI_Handler_Table_Base              
00001b38  __TI_Handler_Table_Limit             
00001b40  __TI_CINIT_Base                      
00001b50  __TI_CINIT_Limit                     
00001b50  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  ExISR_Flag                           
202000f4  Motor_Left                           
20200114  Motor_Right                          
20200134  Data_MotorEncoder                    
20200138  delayTick                            
2020013c  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[133 symbols]
