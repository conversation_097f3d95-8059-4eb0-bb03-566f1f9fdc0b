******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 20:36:08 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001671


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001e80  0001e180  R  X
  SRAM                  20200000   00008000  00000342  00007cbe  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001e80   00001e80    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001ce8   00001ce8    r-x .text
  00001da8    00001da8    00000088   00000088    r-- .rodata
  00001e30    00001e30    00000050   00000050    r-- .cinit
20200000    20200000    00000142   00000000    rw-
  20200000    20200000    000000f4   00000000    rw- .bss
  202000f4    202000f4    0000004e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001ce8     
                  000000c0    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000002a0    000001b0     Task.o (.text.Task_Start)
                  00000450    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00000584    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000688    000000f0     Motor.o (.text.Motor_SetDirc)
                  00000778    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00000854    000000b4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00000908    000000b4     Task.o (.text.Task_Add)
                  000009bc    000000a8     Motor.o (.text.Motor_SetSpeed)
                  00000a64    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000b04    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00000b90    00000088     Task_App.o (.text.Task_Motor_ManualControl)
                  00000c18    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00000c9c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00000d20    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000d9c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00000e00    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000e64    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00000ec6    00000002     Task_App.o (.text.Task_IdleFunction)
                  00000ec8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000f24    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00000f7c    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00000fcc    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00001018    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00001064    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000010b0    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000010fc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00001146    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001148    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001190    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000011d4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00001216    00000002     --HOLE-- [fill = 0]
                  00001218    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00001258    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001298    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000012d8    0000003e     Task.o (.text.Task_CMP)
                  00001316    00000002     --HOLE-- [fill = 0]
                  00001318    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00001354    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001390    0000003c     libclang_rt.builtins.a : comparesf2.S.obj (.text.__gtsf2)
                  000013cc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001408    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001442    00000002     --HOLE-- [fill = 0]
                  00001444    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001478    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  000014a8    0000002c     Interrupt.o (.text.Interrupt_Init)
                  000014d4    0000002c     Motor.o (.text.Motor_Start)
                  00001500    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000152c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00001558    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001580    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000015a8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  000015d0    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000015f8    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00001620    00000028     SysTick.o (.text.SysTick_Increasment)
                  00001648    00000028     Task_App.o (.text.Task_Init)
                  00001670    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001698    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000016be    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000016e4    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00001708    00000024     Motor.o (.text.Motor_SetBothSpeed)
                  0000172c    00000024     Motor.o (.text.Motor_StopAll)
                  00001750    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001770    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00001790    00000020     main.o (.text.main)
                  000017b0    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000017ce    00000002     --HOLE-- [fill = 0]
                  000017d0    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000017ec    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00001808    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00001824    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001840    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000185c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001878    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001894    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  000018b0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000018cc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000018e8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001904    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001920    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000193c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00001954    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000196c    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00001984    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000199c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000019b4    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000019cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000019e4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000019fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001a14    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00001a2c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00001a44    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001a5c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00001a74    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001a8c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001aa4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001abc    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001ad4    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001aec    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00001b04    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00001b1c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001b34    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001b4c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001b64    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001b7c    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00001b94    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00001bac    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00001bc4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00001bdc    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001bf4    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001c0c    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00001c24    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00001c3a    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00001c50    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001c66    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001c7c    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00001c90    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001ca4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00001cb8    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001ccc    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001ce0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001cf4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001d06    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001d18    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001d2a    00000002     --HOLE-- [fill = 0]
                  00001d2c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001d3c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001d4c    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001d5c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00001d68    0000000c     SysTick.o (.text.Sys_GetTick)
                  00001d74    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001d7e    00000008     Interrupt.o (.text.SysTick_Handler)
                  00001d86    00000002     --HOLE-- [fill = 0]
                  00001d88    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001d90    00000006     libc.a : exit.c.obj (.text:abort)
                  00001d96    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001d9a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001d9e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001da2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001da6    00000002     --HOLE-- [fill = 0]

.cinit     0    00001e30    00000050     
                  00001e30    0000002c     (.cinit..data.load) [load image, compression = lzss]
                  00001e5c    0000000c     (__TI_handler_table)
                  00001e68    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001e70    00000010     (__TI_cinit_table)

.rodata    0    00001da8    00000088     
                  00001da8    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001dd0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00001de8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00001e00    0000000c     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00001e0c    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00001e16    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00001e18    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00001e20    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00001e28    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00001e2b    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001e2d    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00001e2f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000000f4     UNINITIALIZED
                  20200000    000000f0     Task.o (.bss.Task_Schedule)
                  202000f0    00000004     (.common:ExISR_Flag)

.data      0    202000f4    0000004e     UNINITIALIZED
                  202000f4    00000020     Motor.o (.data.Motor_Left)
                  20200114    00000020     Motor.o (.data.Motor_Right)
                  20200134    00000004     Task_App.o (.data.Data_MotorEncoder)
                  20200138    00000004     SysTick.o (.data.delayTick)
                  2020013c    00000004     SysTick.o (.data.uwTick)
                  20200140    00000001     Task_App.o (.data.Task_Motor_ManualControl.test_cycle)
                  20200141    00000001     Task.o (.data.Task_Num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             3426   123       0      
       startup_mspm0g350x_ticlang.o   8      192       0      
       main.o                         32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         3466   315       0      
                                                              
    .\APP\Src\
       Interrupt.o                    378    0         4      
       Task_App.o                     178    12        5      
    +--+------------------------------+------+---------+---------+
       Total:                         556    12        9      
                                                              
    .\BSP\Src\
       Task.o                         674    0         241    
       Motor.o                        592    0         64     
       SysTick.o                      52     0         8      
    +--+------------------------------+------+---------+---------+
       Total:                         1318   0         313    
                                                              
    C:/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388    0         0      
       dl_timer.o                     356    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_i2c.o                       38     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1022   0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       qsort.c.obj                    308    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         600    0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       fixunsdfsi.S.obj               66     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         420    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      80        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   7386   407       834    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001e70 records: 2, size/record: 8, table size: 16
	.data: load addr=00001e30, load size=0000002c bytes, run addr=202000f4, run size=0000004e bytes, compression=lzss
	.bss: load addr=00001e68, load size=00000008 bytes, run addr=20200000, run size=000000f4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001e5c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001d97  ADC0_IRQHandler                      
00001d97  ADC1_IRQHandler                      
00001d97  AES_IRQHandler                       
00001d9a  C$$EXIT                              
00001d97  CANFD0_IRQHandler                    
00001d97  DAC0_IRQHandler                      
00001219  DL_ADC12_setClockConfig              
00001d75  DL_Common_delayCycles                
00001019  DL_DMA_initChannel                   
000016bf  DL_I2C_setClockConfig                
00000779  DL_SYSCTL_configSYSPLL               
00000d9d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00001191  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000585  DL_Timer_initFourCCPWMMode           
000018e9  DL_Timer_setCaptCompUpdateMethod     
00001b65  DL_Timer_setCaptureCompareOutCtl     
00001d3d  DL_Timer_setCaptureCompareValue      
00001905  DL_Timer_setClockConfig              
00001149  DL_UART_init                         
00001cf5  DL_UART_setClockConfig               
00001d97  DMA_IRQHandler                       
20200134  Data_MotorEncoder                    
00001d97  Default_Handler                      
202000f0  ExISR_Flag                           
00001d97  GROUP0_IRQHandler                    
00000855  GROUP1_IRQHandler                    
00001d9b  HOSTexit                             
00001d97  HardFault_Handler                    
00001d97  I2C0_IRQHandler                      
00001d97  I2C1_IRQHandler                      
000014a9  Interrupt_Init                       
202000f4  Motor_Left                           
20200114  Motor_Right                          
00001709  Motor_SetBothSpeed                   
000009bd  Motor_SetSpeed                       
000014d5  Motor_Start                          
0000172d  Motor_StopAll                        
00001d97  NMI_Handler                          
00001d97  PendSV_Handler                       
00001d97  RTC_IRQHandler                       
00001d9f  Reset_Handler                        
00001d97  SPI0_IRQHandler                      
00001d97  SPI1_IRQHandler                      
00001d97  SVC_Handler                          
000010b1  SYSCFG_DL_ADC1_init                  
00001479  SYSCFG_DL_DMA_CH_RX_init             
00001c0d  SYSCFG_DL_DMA_CH_TX_init             
00001d5d  SYSCFG_DL_DMA_init                   
000000c1  SYSCFG_DL_GPIO_init                  
00000f25  SYSCFG_DL_I2C_MPU6050_init           
00000e01  SYSCFG_DL_I2C_OLED_init              
00000b05  SYSCFG_DL_Motor_PWM_init             
00000ec9  SYSCFG_DL_SYSCTL_init                
00001d4d  SYSCFG_DL_SYSTICK_init               
00000c19  SYSCFG_DL_UART0_init                 
00001501  SYSCFG_DL_init                       
00000a65  SYSCFG_DL_initPower                  
00001d7f  SysTick_Handler                      
00001621  SysTick_Increasment                  
00001d69  Sys_GetTick                          
00001d97  TIMA0_IRQHandler                     
00001d97  TIMA1_IRQHandler                     
00001d97  TIMG0_IRQHandler                     
00001d97  TIMG12_IRQHandler                    
00001d97  TIMG6_IRQHandler                     
00001d97  TIMG7_IRQHandler                     
00001d97  TIMG8_IRQHandler                     
00001d07  TI_memcpy_small                      
00000909  Task_Add                             
00000ec7  Task_IdleFunction                    
00001649  Task_Init                            
00000b91  Task_Motor_ManualControl             
000002a1  Task_Start                           
00001d97  UART0_IRQHandler                     
00001d97  UART1_IRQHandler                     
00001d97  UART2_IRQHandler                     
00001d97  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001e70  __TI_CINIT_Base                      
00001e80  __TI_CINIT_Limit                     
00001e80  __TI_CINIT_Warm                      
00001e5c  __TI_Handler_Table_Base              
00001e68  __TI_Handler_Table_Limit             
000013cd  __TI_auto_init_nobinit_nopinit       
00000d21  __TI_decompress_lzss                 
00001d19  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00001c67  __TI_zero_init_nomemset              
000011d5  __aeabi_d2uiz                        
00001299  __aeabi_f2d                          
00000e65  __aeabi_fcmpeq                       
00000ea1  __aeabi_fcmpge                       
00000eb5  __aeabi_fcmpgt                       
00000e8d  __aeabi_fcmple                       
00000e79  __aeabi_fcmplt                       
00001147  __aeabi_idiv0                        
00001d89  __aeabi_memcpy                       
00001d89  __aeabi_memcpy4                      
00001d89  __aeabi_memcpy8                      
00001259  __aeabi_uidiv                        
00001259  __aeabi_uidivmod                     
ffffffff  __binit__                            
00001409  __cmpsf2                             
00001409  __eqsf2                              
00001299  __extendsfdf2                        
000011d5  __fixunsdfsi                         
00001391  __gesf2                              
00001391  __gtsf2                              
00001409  __lesf2                              
00001409  __ltsf2                              
UNDEFED   __mpu_init                           
00001409  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001671  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00001da3  _system_pre_init                     
00001d91  abort                                
ffffffff  binit                                
20200138  delayTick                            
00000000  interruptVectors                     
00001791  main                                 
00000451  qsort                                
2020013c  uwTick                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  SYSCFG_DL_GPIO_init                  
00000200  __STACK_SIZE                         
000002a1  Task_Start                           
00000451  qsort                                
00000585  DL_Timer_initFourCCPWMMode           
00000779  DL_SYSCTL_configSYSPLL               
00000855  GROUP1_IRQHandler                    
00000909  Task_Add                             
000009bd  Motor_SetSpeed                       
00000a65  SYSCFG_DL_initPower                  
00000b05  SYSCFG_DL_Motor_PWM_init             
00000b91  Task_Motor_ManualControl             
00000c19  SYSCFG_DL_UART0_init                 
00000d21  __TI_decompress_lzss                 
00000d9d  DL_SYSCTL_setHFCLKSourceHFXTParams   
00000e01  SYSCFG_DL_I2C_OLED_init              
00000e65  __aeabi_fcmpeq                       
00000e79  __aeabi_fcmplt                       
00000e8d  __aeabi_fcmple                       
00000ea1  __aeabi_fcmpge                       
00000eb5  __aeabi_fcmpgt                       
00000ec7  Task_IdleFunction                    
00000ec9  SYSCFG_DL_SYSCTL_init                
00000f25  SYSCFG_DL_I2C_MPU6050_init           
00001019  DL_DMA_initChannel                   
000010b1  SYSCFG_DL_ADC1_init                  
00001147  __aeabi_idiv0                        
00001149  DL_UART_init                         
00001191  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000011d5  __aeabi_d2uiz                        
000011d5  __fixunsdfsi                         
00001219  DL_ADC12_setClockConfig              
00001259  __aeabi_uidiv                        
00001259  __aeabi_uidivmod                     
00001299  __aeabi_f2d                          
00001299  __extendsfdf2                        
00001391  __gesf2                              
00001391  __gtsf2                              
000013cd  __TI_auto_init_nobinit_nopinit       
00001409  __cmpsf2                             
00001409  __eqsf2                              
00001409  __lesf2                              
00001409  __ltsf2                              
00001409  __nesf2                              
00001479  SYSCFG_DL_DMA_CH_RX_init             
000014a9  Interrupt_Init                       
000014d5  Motor_Start                          
00001501  SYSCFG_DL_init                       
00001621  SysTick_Increasment                  
00001649  Task_Init                            
00001671  _c_int00_noargs                      
000016bf  DL_I2C_setClockConfig                
00001709  Motor_SetBothSpeed                   
0000172d  Motor_StopAll                        
00001791  main                                 
000018e9  DL_Timer_setCaptCompUpdateMethod     
00001905  DL_Timer_setClockConfig              
00001b65  DL_Timer_setCaptureCompareOutCtl     
00001c0d  SYSCFG_DL_DMA_CH_TX_init             
00001c67  __TI_zero_init_nomemset              
00001cf5  DL_UART_setClockConfig               
00001d07  TI_memcpy_small                      
00001d19  __TI_decompress_none                 
00001d3d  DL_Timer_setCaptureCompareValue      
00001d4d  SYSCFG_DL_SYSTICK_init               
00001d5d  SYSCFG_DL_DMA_init                   
00001d69  Sys_GetTick                          
00001d75  DL_Common_delayCycles                
00001d7f  SysTick_Handler                      
00001d89  __aeabi_memcpy                       
00001d89  __aeabi_memcpy4                      
00001d89  __aeabi_memcpy8                      
00001d91  abort                                
00001d97  ADC0_IRQHandler                      
00001d97  ADC1_IRQHandler                      
00001d97  AES_IRQHandler                       
00001d97  CANFD0_IRQHandler                    
00001d97  DAC0_IRQHandler                      
00001d97  DMA_IRQHandler                       
00001d97  Default_Handler                      
00001d97  GROUP0_IRQHandler                    
00001d97  HardFault_Handler                    
00001d97  I2C0_IRQHandler                      
00001d97  I2C1_IRQHandler                      
00001d97  NMI_Handler                          
00001d97  PendSV_Handler                       
00001d97  RTC_IRQHandler                       
00001d97  SPI0_IRQHandler                      
00001d97  SPI1_IRQHandler                      
00001d97  SVC_Handler                          
00001d97  TIMA0_IRQHandler                     
00001d97  TIMA1_IRQHandler                     
00001d97  TIMG0_IRQHandler                     
00001d97  TIMG12_IRQHandler                    
00001d97  TIMG6_IRQHandler                     
00001d97  TIMG7_IRQHandler                     
00001d97  TIMG8_IRQHandler                     
00001d97  UART0_IRQHandler                     
00001d97  UART1_IRQHandler                     
00001d97  UART2_IRQHandler                     
00001d97  UART3_IRQHandler                     
00001d9a  C$$EXIT                              
00001d9b  HOSTexit                             
00001d9f  Reset_Handler                        
00001da3  _system_pre_init                     
00001e5c  __TI_Handler_Table_Base              
00001e68  __TI_Handler_Table_Limit             
00001e70  __TI_CINIT_Base                      
00001e80  __TI_CINIT_Limit                     
00001e80  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202000f0  ExISR_Flag                           
202000f4  Motor_Left                           
20200114  Motor_Right                          
20200134  Data_MotorEncoder                    
20200138  delayTick                            
2020013c  uwTick                               
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[140 symbols]
