<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -IC:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o TI_CAR1.out -mTI_CAR1.map -iC:/ti/mspm0_sdk_2_04_00_06/source -iC:/Users/<USER>/Desktop/009/008 -iC:/Users/<USER>/Desktop/009/008/Debug/syscfg -iC:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=TI_CAR1_linkInfo.xml --rom_model ./Manual_Motor_Demo.o ./Motor_Control_Example.o ./Test_Manual_Control.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./main.o ./APP/Src/Interrupt.o ./APP/Src/Motor_Control.o ./APP/Src/Task_App.o ./BSP/Src/Motor.o ./BSP/Src/PID_IQMath.o ./BSP/Src/SysTick.o ./BSP/Src/Task.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688cb624</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\Desktop\009\008\Debug\TI_CAR1.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1359</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>Manual_Motor_Demo.o</file>
         <name>Manual_Motor_Demo.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>Motor_Control_Example.o</file>
         <name>Motor_Control_Example.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>Test_Manual_Control.o</file>
         <name>Test_Manual_Control.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\APP\Src\</path>
         <kind>object</kind>
         <file>Interrupt.o</file>
         <name>Interrupt.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\APP\Src\</path>
         <kind>object</kind>
         <file>Motor_Control.o</file>
         <name>Motor_Control.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\APP\Src\</path>
         <kind>object</kind>
         <file>Task_App.o</file>
         <name>Task_App.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>Motor.o</file>
         <name>Motor.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>PID_IQMath.o</file>
         <name>PID_IQMath.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>SysTick.o</file>
         <name>SysTick.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\.\BSP\Src\</path>
         <kind>object</kind>
         <file>Task.o</file>
         <name>Task.o</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\Users\<USER>\Desktop\009\008\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\iqmath\lib\ticlang\m0p\mathacl\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>iqmath.a</file>
         <name>_IQNdiv.o</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\iqmath\lib\ticlang\m0p\mathacl\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>iqmath.a</file>
         <name>_IQNmpy.o</name>
      </input_file>
      <input_file id="fl-1e">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\iqmath\lib\ticlang\m0p\mathacl\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>iqmath.a</file>
         <name>_IQNtables.o</name>
      </input_file>
      <input_file id="fl-1f">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-20">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_04_00_06\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-3c">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>qsort.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-e7">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-e8">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-e9">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-ea">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-eb">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-ec">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixunsdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-ed">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-ee">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-ef">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-f0">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-f1">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x1e0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.Task_Start</name>
         <load_address>0x2a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2a0</run_address>
         <size>0x1b0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x450</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x450</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.Motor_SetDirc</name>
         <load_address>0x554</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x554</run_address>
         <size>0xf0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x644</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x720</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x720</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.Motor_SetSpeed</name>
         <load_address>0x7d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d4</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x87c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87c</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.SYSCFG_DL_Motor_PWM_init</name>
         <load_address>0x91c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x91c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_UART0_init</name>
         <load_address>0x9a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9a8</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0xa2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa2c</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xab0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xab0</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0xb2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb2c</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0xb90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb90</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0xbf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbf4</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ee"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.Task_IdleFunction</name>
         <load_address>0xc56</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc56</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xc58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc58</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.SYSCFG_DL_I2C_MPU6050_init</name>
         <load_address>0xcb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcb4</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.SysTick_Config</name>
         <load_address>0xd0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd0c</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0xd5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd5c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0xda8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xda8</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0xdf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf4</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_ADC1_init</name>
         <load_address>0xe40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe40</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0xe8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe8c</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_UART_init</name>
         <load_address>0xed8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0xf20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf20</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.__fixunsdfsi</name>
         <load_address>0xf64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf64</run_address>
         <size>0x42</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-153">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0xfa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.__extendsfdf2</name>
         <load_address>0xfe8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0x1028</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1028</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x1064</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1064</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.__gtsf2</name>
         <load_address>0x10a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10a0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x10dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10dc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.__eqsf2</name>
         <load_address>0x1118</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1118</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1154</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1154</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.SYSCFG_DL_DMA_CH_RX_init</name>
         <load_address>0x1188</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1188</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.Interrupt_Init</name>
         <load_address>0x11b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11b8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.Motor_Start</name>
         <load_address>0x11e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11e4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1210</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1210</run_address>
         <size>0x2c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-162">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x123c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x123c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-171">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1268</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1268</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0x1290</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1290</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-139">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0x12b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12b8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_UART_setRXFIFOThreshold</name>
         <load_address>0x12e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12e0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_UART_setTXFIFOThreshold</name>
         <load_address>0x1308</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1308</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.SysTick_Increasment</name>
         <load_address>0x1330</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1330</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1358</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1358</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0x1380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1380</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x13a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a6</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_UART_setRXInterruptTimeout</name>
         <load_address>0x13cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13cc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.Motor_StopAll</name>
         <load_address>0x13f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13f0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1414</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1414</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0x1434</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1434</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text.main</name>
         <load_address>0x1454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1454</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1474</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1474</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_DMA_enableInterrupt</name>
         <load_address>0x1494</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1494</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-66">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x14b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14b0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x14cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14cc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0x14e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14e8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x1504</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1504</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1520</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_I2C_enableInterrupt</name>
         <load_address>0x153c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x153c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.DL_Interrupt_getPendingGroup</name>
         <load_address>0x1558</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1558</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1574</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1574</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1590</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x15ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15ac</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x15c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15c8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x15e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15e4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x1600</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1600</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x1618</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1618</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_DMA_clearInterruptStatus</name>
         <load_address>0x1630</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1630</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1648</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1648</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1660</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1660</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1678</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1678</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1690</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1690</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_GPIO_initPeripheralAnalogFunction</name>
         <load_address>0x16a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16a8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x16c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16c0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x16d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16d8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x16f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x1708</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1708</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0x1720</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1720</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0x1738</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1738</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0x1750</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1750</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0x1768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1768</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x1780</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1780</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-138">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0x1798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1798</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.DL_MathACL_enablePower</name>
         <load_address>0x17b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17b0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_MathACL_reset</name>
         <load_address>0x17c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x17e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17e0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x17f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17f8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1810</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1810</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1828</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1828</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x1840</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1840</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_UART_enableDMAReceiveEvent</name>
         <load_address>0x1858</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1858</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_UART_enableDMATransmitEvent</name>
         <load_address>0x1870</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1870</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_UART_enableFIFOs</name>
         <load_address>0x1888</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1888</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x18a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_UART_reset</name>
         <load_address>0x18b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.SYSCFG_DL_DMA_CH_TX_init</name>
         <load_address>0x18d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x18e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18e8</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x18fe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18fe</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1914</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1914</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x192a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x192a</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1940</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1940</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1954</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1954</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0x1968</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1968</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x197c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x197c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1990</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1990</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x19a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19a4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x19b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19b8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x19ca</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19ca</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x19dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19dc</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x19f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19f0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x1a00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a00</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x1a10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a10</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.Task_Init</name>
         <load_address>0x1a20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a20</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x1a30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a30</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.text.Sys_GetTick</name>
         <load_address>0x1a3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a3c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1a48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a48</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0x1a52</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a52</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1a5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a5c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ef"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text:abort</name>
         <load_address>0x1a64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a64</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1a6a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a6a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.HOSTexit</name>
         <load_address>0x1a6e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a6e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1a72</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a72</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text._system_pre_init</name>
         <load_address>0x1a76</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a76</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.cinit..data.load</name>
         <load_address>0x1b00</load_address>
         <readonly>true</readonly>
         <run_address>0x1b00</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1df">
         <name>__TI_handler_table</name>
         <load_address>0x1b2c</load_address>
         <readonly>true</readonly>
         <run_address>0x1b2c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1e2">
         <name>.cinit..bss.load</name>
         <load_address>0x1b38</load_address>
         <readonly>true</readonly>
         <run_address>0x1b38</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1e0">
         <name>__TI_cinit_table</name>
         <load_address>0x1b40</load_address>
         <readonly>true</readonly>
         <run_address>0x1b40</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-11f">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x1a80</load_address>
         <readonly>true</readonly>
         <run_address>0x1a80</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-182">
         <name>.rodata.gDMA_CH_RXConfig</name>
         <load_address>0x1aa8</load_address>
         <readonly>true</readonly>
         <run_address>0x1aa8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-183">
         <name>.rodata.gDMA_CH_TXConfig</name>
         <load_address>0x1ac0</load_address>
         <readonly>true</readonly>
         <run_address>0x1ac0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-152">
         <name>.rodata.gUART0Config</name>
         <load_address>0x1ad8</load_address>
         <readonly>true</readonly>
         <run_address>0x1ad8</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.rodata.gI2C_MPU6050ClockConfig</name>
         <load_address>0x1ae2</load_address>
         <readonly>true</readonly>
         <run_address>0x1ae2</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.rodata.gADC1ClockConfig</name>
         <load_address>0x1ae4</load_address>
         <readonly>true</readonly>
         <run_address>0x1ae4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.rodata.gMotor_PWMConfig</name>
         <load_address>0x1aec</load_address>
         <readonly>true</readonly>
         <run_address>0x1aec</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.rodata.gMotor_PWMClockConfig</name>
         <load_address>0x1af4</load_address>
         <readonly>true</readonly>
         <run_address>0x1af4</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1af7</load_address>
         <readonly>true</readonly>
         <run_address>0x1af7</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-151">
         <name>.rodata.gUART0ClockConfig</name>
         <load_address>0x1af9</load_address>
         <readonly>true</readonly>
         <run_address>0x1af9</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-8a">
         <name>.data.Data_MotorEncoder</name>
         <load_address>0x20200134</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200134</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-68">
         <name>.data.Motor_Left</name>
         <load_address>0x202000f4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f4</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.data.Motor_Right</name>
         <load_address>0x20200114</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200114</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-86">
         <name>.data.uwTick</name>
         <load_address>0x2020013c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020013c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-87">
         <name>.data.delayTick</name>
         <load_address>0x20200138</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200138</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-de">
         <name>.data.Task_Num</name>
         <load_address>0x20200140</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-df">
         <name>.bss.Task_Schedule</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xf0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-67">
         <name>.common:ExISR_Flag</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x1e9</load_address>
         <run_address>0x1e9</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_abbrev</name>
         <load_address>0x256</load_address>
         <run_address>0x256</run_address>
         <size>0x47</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0x29d</load_address>
         <run_address>0x29d</run_address>
         <size>0x160</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_abbrev</name>
         <load_address>0x3fd</load_address>
         <run_address>0x3fd</run_address>
         <size>0x112</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_abbrev</name>
         <load_address>0x50f</load_address>
         <run_address>0x50f</run_address>
         <size>0x15e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x66d</load_address>
         <run_address>0x66d</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_abbrev</name>
         <load_address>0x739</load_address>
         <run_address>0x739</run_address>
         <size>0x175</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_abbrev</name>
         <load_address>0x8ae</load_address>
         <run_address>0x8ae</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_abbrev</name>
         <load_address>0xa1f</load_address>
         <run_address>0xa1f</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-191">
         <name>.debug_abbrev</name>
         <load_address>0xa81</load_address>
         <run_address>0xa81</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_abbrev</name>
         <load_address>0xc01</load_address>
         <run_address>0xc01</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_abbrev</name>
         <load_address>0xde8</load_address>
         <run_address>0xde8</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_abbrev</name>
         <load_address>0x106e</load_address>
         <run_address>0x106e</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_abbrev</name>
         <load_address>0x1309</load_address>
         <run_address>0x1309</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x1521</load_address>
         <run_address>0x1521</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0x15d0</load_address>
         <run_address>0x15d0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_abbrev</name>
         <load_address>0x1740</load_address>
         <run_address>0x1740</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_abbrev</name>
         <load_address>0x1779</load_address>
         <run_address>0x1779</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x183b</load_address>
         <run_address>0x183b</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x18ab</load_address>
         <run_address>0x18ab</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0x1938</load_address>
         <run_address>0x1938</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_abbrev</name>
         <load_address>0x19d0</load_address>
         <run_address>0x19d0</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_abbrev</name>
         <load_address>0x19fc</load_address>
         <run_address>0x19fc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_abbrev</name>
         <load_address>0x1a23</load_address>
         <run_address>0x1a23</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_abbrev</name>
         <load_address>0x1a4a</load_address>
         <run_address>0x1a4a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ee"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_abbrev</name>
         <load_address>0x1a71</load_address>
         <run_address>0x1a71</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ef"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_abbrev</name>
         <load_address>0x1a98</load_address>
         <run_address>0x1a98</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0x1abd</load_address>
         <run_address>0x1abd</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_abbrev</name>
         <load_address>0x1ae2</load_address>
         <run_address>0x1ae2</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4775</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4775</load_address>
         <run_address>0x4775</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x47f5</load_address>
         <run_address>0x47f5</run_address>
         <size>0x65</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x485a</load_address>
         <run_address>0x485a</run_address>
         <size>0xbcd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_info</name>
         <load_address>0x5427</load_address>
         <run_address>0x5427</run_address>
         <size>0x830</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_info</name>
         <load_address>0x5c57</load_address>
         <run_address>0x5c57</run_address>
         <size>0xfa6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x6bfd</load_address>
         <run_address>0x6bfd</run_address>
         <size>0xf2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0x6cef</load_address>
         <run_address>0x6cef</run_address>
         <size>0x4cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-154">
         <name>.debug_info</name>
         <load_address>0x71be</load_address>
         <run_address>0x71be</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_info</name>
         <load_address>0x7903</load_address>
         <run_address>0x7903</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_info</name>
         <load_address>0x7978</load_address>
         <run_address>0x7978</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_info</name>
         <load_address>0x8062</load_address>
         <run_address>0x8062</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_info</name>
         <load_address>0x8d24</load_address>
         <run_address>0x8d24</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0xbe96</load_address>
         <run_address>0xbe96</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_info</name>
         <load_address>0xd13c</load_address>
         <run_address>0xd13c</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xe1cc</load_address>
         <run_address>0xe1cc</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_info</name>
         <load_address>0xe5ef</load_address>
         <run_address>0xe5ef</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_info</name>
         <load_address>0xed33</load_address>
         <run_address>0xed33</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_info</name>
         <load_address>0xed79</load_address>
         <run_address>0xed79</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xef0b</load_address>
         <run_address>0xef0b</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xefd1</load_address>
         <run_address>0xefd1</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_info</name>
         <load_address>0xf14d</load_address>
         <run_address>0xf14d</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_info</name>
         <load_address>0xf245</load_address>
         <run_address>0xf245</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_info</name>
         <load_address>0xf280</load_address>
         <run_address>0xf280</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_info</name>
         <load_address>0xf417</load_address>
         <run_address>0xf417</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_info</name>
         <load_address>0xf5ac</load_address>
         <run_address>0xf5ac</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ee"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0xf7c3</load_address>
         <run_address>0xf7c3</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ef"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_info</name>
         <load_address>0xf95c</load_address>
         <run_address>0xf95c</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_info</name>
         <load_address>0xfb1d</load_address>
         <run_address>0xfb1d</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_info</name>
         <load_address>0xfe17</load_address>
         <run_address>0xfe17</run_address>
         <size>0x97</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x270</load_address>
         <run_address>0x270</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x2b8</load_address>
         <run_address>0x2b8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_ranges</name>
         <load_address>0x2d8</load_address>
         <run_address>0x2d8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_ranges</name>
         <load_address>0x328</load_address>
         <run_address>0x328</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_ranges</name>
         <load_address>0x350</load_address>
         <run_address>0x350</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-155">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_ranges</name>
         <load_address>0x3b8</load_address>
         <run_address>0x3b8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_ranges</name>
         <load_address>0x590</load_address>
         <run_address>0x590</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-141">
         <name>.debug_ranges</name>
         <load_address>0x768</load_address>
         <run_address>0x768</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_ranges</name>
         <load_address>0x910</load_address>
         <run_address>0x910</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0xab8</load_address>
         <run_address>0xab8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_ranges</name>
         <load_address>0xb00</load_address>
         <run_address>0xb00</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0xb48</load_address>
         <run_address>0xb48</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0xb60</load_address>
         <run_address>0xb60</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_ranges</name>
         <load_address>0xbb0</load_address>
         <run_address>0xbb0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-198">
         <name>.debug_ranges</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_ranges</name>
         <load_address>0xc00</load_address>
         <run_address>0xc00</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3ac4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x3ac4</load_address>
         <run_address>0x3ac4</run_address>
         <size>0x14e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_str</name>
         <load_address>0x3c12</load_address>
         <run_address>0x3c12</run_address>
         <size>0xd3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_str</name>
         <load_address>0x3ce5</load_address>
         <run_address>0x3ce5</run_address>
         <size>0x826</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_str</name>
         <load_address>0x450b</load_address>
         <run_address>0x450b</run_address>
         <size>0x4ff</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_str</name>
         <load_address>0x4a0a</load_address>
         <run_address>0x4a0a</run_address>
         <size>0x799</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0x51a3</load_address>
         <run_address>0x51a3</run_address>
         <size>0x122</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_str</name>
         <load_address>0x52c5</load_address>
         <run_address>0x52c5</run_address>
         <size>0x318</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_str</name>
         <load_address>0x55dd</load_address>
         <run_address>0x55dd</run_address>
         <size>0x63b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_str</name>
         <load_address>0x5c18</load_address>
         <run_address>0x5c18</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_str</name>
         <load_address>0x5d8f</load_address>
         <run_address>0x5d8f</run_address>
         <size>0x654</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_str</name>
         <load_address>0x63e3</load_address>
         <run_address>0x63e3</run_address>
         <size>0x8b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_str</name>
         <load_address>0x6c9c</load_address>
         <run_address>0x6c9c</run_address>
         <size>0x1dd6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_str</name>
         <load_address>0x8a72</load_address>
         <run_address>0x8a72</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_str</name>
         <load_address>0x975f</load_address>
         <run_address>0x975f</run_address>
         <size>0x107f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0xa7de</load_address>
         <run_address>0xa7de</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_str</name>
         <load_address>0xaa03</load_address>
         <run_address>0xaa03</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_str</name>
         <load_address>0xad32</load_address>
         <run_address>0xad32</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_str</name>
         <load_address>0xae27</load_address>
         <run_address>0xae27</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0xafc2</load_address>
         <run_address>0xafc2</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0xb12a</load_address>
         <run_address>0xb12a</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0xb2ff</load_address>
         <run_address>0xb2ff</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_str</name>
         <load_address>0xb447</load_address>
         <run_address>0xb447</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6bc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x6bc</load_address>
         <run_address>0x6bc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_frame</name>
         <load_address>0x6ec</load_address>
         <run_address>0x6ec</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_frame</name>
         <load_address>0x7d8</load_address>
         <run_address>0x7d8</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_frame</name>
         <load_address>0x82c</load_address>
         <run_address>0x82c</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x920</load_address>
         <run_address>0x920</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_frame</name>
         <load_address>0x980</load_address>
         <run_address>0x980</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_frame</name>
         <load_address>0xa50</load_address>
         <run_address>0xa50</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_frame</name>
         <load_address>0xa9c</load_address>
         <run_address>0xa9c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_frame</name>
         <load_address>0xabc</load_address>
         <run_address>0xabc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_frame</name>
         <load_address>0xaec</load_address>
         <run_address>0xaec</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_frame</name>
         <load_address>0xc18</load_address>
         <run_address>0xc18</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_frame</name>
         <load_address>0x1020</load_address>
         <run_address>0x1020</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_frame</name>
         <load_address>0x11d8</load_address>
         <run_address>0x11d8</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x1304</load_address>
         <run_address>0x1304</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_frame</name>
         <load_address>0x1394</load_address>
         <run_address>0x1394</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_frame</name>
         <load_address>0x1494</load_address>
         <run_address>0x1494</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x14b4</load_address>
         <run_address>0x14b4</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x14ec</load_address>
         <run_address>0x14ec</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x1514</load_address>
         <run_address>0x1514</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_frame</name>
         <load_address>0x1544</load_address>
         <run_address>0x1544</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_frame</name>
         <load_address>0x1574</load_address>
         <run_address>0x1574</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x10e7</load_address>
         <run_address>0x10e7</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_line</name>
         <load_address>0x119f</load_address>
         <run_address>0x119f</run_address>
         <size>0x47</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x11e6</load_address>
         <run_address>0x11e6</run_address>
         <size>0x42b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x1611</load_address>
         <run_address>0x1611</run_address>
         <size>0x1ed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_line</name>
         <load_address>0x17fe</load_address>
         <run_address>0x17fe</run_address>
         <size>0x46e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x1c6c</load_address>
         <run_address>0x1c6c</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x1de5</load_address>
         <run_address>0x1de5</run_address>
         <size>0x61f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_line</name>
         <load_address>0x2404</load_address>
         <run_address>0x2404</run_address>
         <size>0x280</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_line</name>
         <load_address>0x2684</load_address>
         <run_address>0x2684</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_line</name>
         <load_address>0x27fd</load_address>
         <run_address>0x27fd</run_address>
         <size>0x249</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_line</name>
         <load_address>0x2a46</load_address>
         <run_address>0x2a46</run_address>
         <size>0x683</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_line</name>
         <load_address>0x30c9</load_address>
         <run_address>0x30c9</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_line</name>
         <load_address>0x4838</load_address>
         <run_address>0x4838</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_line</name>
         <load_address>0x5250</load_address>
         <run_address>0x5250</run_address>
         <size>0x983</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x5bd3</load_address>
         <run_address>0x5bd3</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x5daf</load_address>
         <run_address>0x5daf</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_line</name>
         <load_address>0x62c9</load_address>
         <run_address>0x62c9</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x6307</load_address>
         <run_address>0x6307</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x6405</load_address>
         <run_address>0x6405</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x64c5</load_address>
         <run_address>0x64c5</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_line</name>
         <load_address>0x668d</load_address>
         <run_address>0x668d</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_line</name>
         <load_address>0x66f4</load_address>
         <run_address>0x66f4</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e7"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_line</name>
         <load_address>0x6735</load_address>
         <run_address>0x6735</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_line</name>
         <load_address>0x67f5</load_address>
         <run_address>0x67f5</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-187">
         <name>.debug_line</name>
         <load_address>0x68b5</load_address>
         <run_address>0x68b5</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ee"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x697c</load_address>
         <run_address>0x697c</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ef"/>
      </object_component>
      <object_component id="oc-197">
         <name>.debug_line</name>
         <load_address>0x6a20</load_address>
         <run_address>0x6a20</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_line</name>
         <load_address>0x6b24</load_address>
         <run_address>0x6b24</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_loc</name>
         <load_address>0x1aa</load_address>
         <run_address>0x1aa</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_loc</name>
         <load_address>0x4fc</load_address>
         <run_address>0x4fc</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_loc</name>
         <load_address>0x1f23</load_address>
         <run_address>0x1f23</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_loc</name>
         <load_address>0x26df</load_address>
         <run_address>0x26df</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x2af3</load_address>
         <run_address>0x2af3</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_loc</name>
         <load_address>0x2bcb</load_address>
         <run_address>0x2bcb</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_loc</name>
         <load_address>0x2fef</load_address>
         <run_address>0x2fef</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x315b</load_address>
         <run_address>0x315b</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x31ca</load_address>
         <run_address>0x31ca</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x3331</load_address>
         <run_address>0x3331</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ea"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ec"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ee"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ef"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f2"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_aranges</name>
         <load_address>0xb0</load_address>
         <run_address>0xb0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f5"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x19c0</size>
         <contents>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-74"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1b00</load_address>
         <run_address>0x1b00</run_address>
         <size>0x50</size>
         <contents>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-1e0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1a80</load_address>
         <run_address>0x1a80</run_address>
         <size>0x80</size>
         <contents>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-151"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202000f4</run_address>
         <size>0x4d</size>
         <contents>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-de"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0xf4</size>
         <contents>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-67"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1e4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a0" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a1" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a2" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a3" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a4" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a5" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a7" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c3" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1af1</size>
         <contents>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-1e6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c5" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfeae</size>
         <contents>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-1e5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c7" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc28</size>
         <contents>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-93"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1c9" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb530</size>
         <contents>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-164"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1cb" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1594</size>
         <contents>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-e4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1cd" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6bc4</size>
         <contents>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1cf" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3357</size>
         <contents>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-b1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1d9" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd8</size>
         <contents>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-92"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1e3" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1ef" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b50</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1f0" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x141</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1f1" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1b50</used_space>
         <unused_space>0x1e4b0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x19c0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1a80</start_address>
               <size>0x80</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1b00</start_address>
               <size>0x50</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1b50</start_address>
               <size>0x1e4b0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x341</used_space>
         <unused_space>0x7cbf</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1a5"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1a7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0xf4</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202000f4</start_address>
               <size>0x4d</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200141</start_address>
               <size>0x7cbf</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1b00</load_address>
            <load_size>0x2c</load_size>
            <run_address>0x202000f4</run_address>
            <run_size>0x4d</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1b38</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0xf4</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1b40</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1b50</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1b50</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1b2c</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1b38</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-15e">
         <name>SYSCFG_DL_init</name>
         <value>0x1211</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-15f">
         <name>SYSCFG_DL_initPower</name>
         <value>0x87d</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-160">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-161">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xc59</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-162">
         <name>SYSCFG_DL_Motor_PWM_init</name>
         <value>0x91d</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-163">
         <name>SYSCFG_DL_I2C_MPU6050_init</name>
         <value>0xcb5</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-164">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0xb91</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-165">
         <name>SYSCFG_DL_UART0_init</name>
         <value>0x9a9</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-166">
         <name>SYSCFG_DL_ADC1_init</name>
         <value>0xe41</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-167">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x1a31</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-168">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x1a11</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-169">
         <name>SYSCFG_DL_DMA_CH_RX_init</name>
         <value>0x1189</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-16a">
         <name>SYSCFG_DL_DMA_CH_TX_init</name>
         <value>0x18d1</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-175">
         <name>Default_Handler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-176">
         <name>Reset_Handler</name>
         <value>0x1a73</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-177">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-178">
         <name>NMI_Handler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-179">
         <name>HardFault_Handler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17a">
         <name>SVC_Handler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17b">
         <name>PendSV_Handler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17c">
         <name>GROUP0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17d">
         <name>TIMG8_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17e">
         <name>UART3_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-17f">
         <name>ADC0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-180">
         <name>ADC1_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-181">
         <name>CANFD0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-182">
         <name>DAC0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-183">
         <name>SPI0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-184">
         <name>SPI1_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-185">
         <name>UART1_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-186">
         <name>UART2_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-187">
         <name>UART0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-188">
         <name>TIMG0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-189">
         <name>TIMG6_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18a">
         <name>TIMA0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18b">
         <name>TIMA1_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18c">
         <name>TIMG7_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18d">
         <name>TIMG12_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18e">
         <name>I2C0_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18f">
         <name>I2C1_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-190">
         <name>AES_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-191">
         <name>RTC_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-192">
         <name>DMA_IRQHandler</name>
         <value>0x1a6b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19b">
         <name>main</name>
         <value>0x1455</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>SysTick_Handler</name>
         <value>0x1a53</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-1be">
         <name>GROUP1_IRQHandler</name>
         <value>0x721</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-1bf">
         <name>ExISR_Flag</name>
         <value>0x202000f0</value>
      </symbol>
      <symbol id="sm-1c0">
         <name>Interrupt_Init</name>
         <value>0x11b9</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-1cc">
         <name>Task_Init</name>
         <value>0x1a21</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-1cd">
         <name>Task_IdleFunction</name>
         <value>0xc57</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>Data_MotorEncoder</name>
         <value>0x20200134</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-1ed">
         <name>Motor_Start</name>
         <value>0x11e5</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-1ee">
         <name>Motor_SetSpeed</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-161"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>Motor_Left</name>
         <value>0x202000f4</value>
         <object_component_ref idref="oc-68"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>Motor_Right</name>
         <value>0x20200114</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>Motor_StopAll</name>
         <value>0x13f1</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>SysTick_Increasment</name>
         <value>0x1331</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-200">
         <name>uwTick</name>
         <value>0x2020013c</value>
         <object_component_ref idref="oc-86"/>
      </symbol>
      <symbol id="sm-201">
         <name>delayTick</name>
         <value>0x20200138</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-202">
         <name>Sys_GetTick</name>
         <value>0x1a3d</value>
         <object_component_ref idref="oc-a8"/>
      </symbol>
      <symbol id="sm-210">
         <name>Task_Start</name>
         <value>0x2a1</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-211">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-212">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-213">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-214">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-215">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-216">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-217">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-218">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-219">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-227">
         <name>DL_ADC12_setClockConfig</name>
         <value>0xfa9</value>
         <object_component_ref idref="oc-153"/>
      </symbol>
      <symbol id="sm-230">
         <name>DL_Common_delayCycles</name>
         <value>0x1a49</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-23a">
         <name>DL_DMA_initChannel</name>
         <value>0xda9</value>
         <object_component_ref idref="oc-17e"/>
      </symbol>
      <symbol id="sm-244">
         <name>DL_I2C_setClockConfig</name>
         <value>0x13a7</value>
         <object_component_ref idref="oc-12f"/>
      </symbol>
      <symbol id="sm-25b">
         <name>DL_Timer_setClockConfig</name>
         <value>0x15c9</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-25c">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x1a01</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-25d">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x15ad</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-25e">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1829</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-25f">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x451</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-26c">
         <name>DL_UART_init</name>
         <value>0xed9</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-26d">
         <name>DL_UART_setClockConfig</name>
         <value>0x19b9</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-27e">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x645</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-27f">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0xf21</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-280">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0xb2d</value>
         <object_component_ref idref="oc-115"/>
      </symbol>
      <symbol id="sm-28c">
         <name>_c_int00_noargs</name>
         <value>0x1359</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-28d">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-299">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x10dd</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-2a1">
         <name>_system_pre_init</name>
         <value>0x1a77</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-2ac">
         <name>__TI_zero_init_nomemset</name>
         <value>0x192b</value>
         <object_component_ref idref="oc-4c"/>
      </symbol>
      <symbol id="sm-2b5">
         <name>__TI_decompress_none</name>
         <value>0x19dd</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2c0">
         <name>__TI_decompress_lzss</name>
         <value>0xab1</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2ca">
         <name>abort</name>
         <value>0x1a65</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>HOSTexit</name>
         <value>0x1a6f</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>C$$EXIT</name>
         <value>0x1a6e</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-2db">
         <name>__aeabi_f2d</name>
         <value>0xfe9</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-2dc">
         <name>__extendsfdf2</name>
         <value>0xfe9</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-2e2">
         <name>__aeabi_d2uiz</name>
         <value>0xf65</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-2e3">
         <name>__fixunsdfsi</name>
         <value>0xf65</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-2e9">
         <name>__aeabi_fcmpeq</name>
         <value>0xbf5</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-2ea">
         <name>__aeabi_fcmplt</name>
         <value>0xc09</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-2eb">
         <name>__aeabi_fcmple</name>
         <value>0xc1d</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-2ec">
         <name>__aeabi_fcmpge</name>
         <value>0xc31</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-2ed">
         <name>__aeabi_fcmpgt</name>
         <value>0xc45</value>
         <object_component_ref idref="oc-184"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>__aeabi_memcpy</name>
         <value>0x1a5d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-2f4">
         <name>__aeabi_memcpy4</name>
         <value>0x1a5d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-2f5">
         <name>__aeabi_memcpy8</name>
         <value>0x1a5d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-2fe">
         <name>__eqsf2</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-2ff">
         <name>__lesf2</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-300">
         <name>__ltsf2</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-301">
         <name>__nesf2</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-302">
         <name>__cmpsf2</name>
         <value>0x1119</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-303">
         <name>__gtsf2</name>
         <value>0x10a1</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-304">
         <name>__gesf2</name>
         <value>0x10a1</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-30e">
         <name>TI_memcpy_small</name>
         <value>0x19cb</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-30f">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-312">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-313">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
