[{"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/APP/Src/Interrupt.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/APP/Src/Motor_Control.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/APP/Src/Task_App.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/BSP/Src/Motor.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/BSP/Src/PID_IQMath.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/BSP/Src/SysTick.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/BSP/Src/Task.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/Manual_Motor_Demo.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/Motor_Control_Example.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/Test_Manual_Control.c"}, {"directory": "C:/Users/<USER>/Desktop/009/008/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/Desktop/009/008\" -I\"C:/Users/<USER>/Desktop/009/008/Debug\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_04_00_06/source\" -I\"C:/Users/<USER>/Desktop/009/008/BSP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/APP/Inc\" -I\"C:/Users/<USER>/Desktop/009/008/DMP\" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/Desktop/009/008/main.c"}]